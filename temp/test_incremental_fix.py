#!/usr/bin/env python3
"""
测试增量模式修复效果
验证不完整订单是否正确进入等待表
"""

import sys
import os
sys.path.append('/Users/<USER>/Downloads/risk-link-analysis-tool/backend')

import pandas as pd
from datetime import datetime, timedelta
from modules.contract_risk_analysis.services.incremental_processor import SimpleIncrementalProcessor
from database.duckdb_manager import DuckDBManager

def create_test_data():
    """创建测试数据：包含完整和不完整的订单"""
    
    # 测试数据1：完整订单（有开仓+平仓，数量匹配）
    complete_order = [
        {
            'position_id': 'TEST_COMPLETE_001',
            'member_id': 'user001',
            'contract_name': 'BTCUSDT',
            'side': 1,  # 开多
            'deal_vol': 1.0,
            'deal_vol_usdt': 1000.0,
            'create_time': datetime.now() - timedelta(hours=2),
            'profit': 0.0,
            'liquidity': 'maker'
        },
        {
            'position_id': 'TEST_COMPLETE_001',
            'member_id': 'user001', 
            'contract_name': 'BTCUSDT',
            'side': 4,  # 平多
            'deal_vol': 1.0,
            'deal_vol_usdt': 1050.0,
            'create_time': datetime.now() - timedelta(hours=1),
            'profit': 50.0,
            'liquidity': 'taker'
        }
    ]
    
    # 测试数据2：只有开仓的不完整订单
    incomplete_open_only = [
        {
            'position_id': 'TEST_INCOMPLETE_002',
            'member_id': 'user002',
            'contract_name': 'ETHUSDT',
            'side': 3,  # 开空
            'deal_vol': 2.0,
            'deal_vol_usdt': 2000.0,
            'create_time': datetime.now() - timedelta(hours=3),
            'profit': 0.0,
            'liquidity': 'maker'
        }
    ]
    
    # 测试数据3：只有平仓的不完整订单
    incomplete_close_only = [
        {
            'position_id': 'TEST_INCOMPLETE_003',
            'member_id': 'user003',
            'contract_name': 'ADAUSDT',
            'side': 2,  # 平空
            'deal_vol': 1.5,
            'deal_vol_usdt': 1500.0,
            'create_time': datetime.now() - timedelta(hours=1),
            'profit': 100.0,
            'liquidity': 'taker'
        }
    ]
    
    # 合并所有测试数据
    all_data = complete_order + incomplete_open_only + incomplete_close_only
    return pd.DataFrame(all_data)

def test_incremental_processing():
    """测试增量处理逻辑"""
    
    print("🚀 开始测试增量模式修复效果...")
    
    # 1. 创建测试数据
    test_df = create_test_data()
    print(f"📊 创建测试数据: {len(test_df)} 条记录")
    print(f"   - 完整订单: TEST_COMPLETE_001 (开仓+平仓)")
    print(f"   - 不完整订单1: TEST_INCOMPLETE_002 (只有开仓)")
    print(f"   - 不完整订单2: TEST_INCOMPLETE_003 (只有平仓)")
    
    # 2. 初始化增量处理器
    try:
        processor = SimpleIncrementalProcessor(task_id="test_fix_001")
        print("✅ 增量处理器初始化成功")
    except Exception as e:
        print(f"❌ 增量处理器初始化失败: {e}")
        return

    # 3. 清空等待表（确保测试环境干净）
    try:
        processor.db_manager.execute_sql("DELETE FROM incomplete_positions_waiting")
        print("🧹 清空等待表")
    except Exception as e:
        print(f"⚠️  清空等待表失败: {e}")
    
    # 4. 执行增量处理
    try:
        print("\n🔄 执行增量处理...")
        result = processor.process_new_data(test_df)
        print(f"✅ 增量处理完成，返回完整订单数量: {len(result)}")
        
        # 打印结果详情
        for pos_id, position in result.items():
            print(f"   - {pos_id}: 完整={position.is_completed}, 开仓={position.total_open_amount}, 平仓={position.total_close_amount}")
            
    except Exception as e:
        print(f"❌ 增量处理失败: {e}")
        import traceback
        print(traceback.format_exc())
        return
    
    # 5. 检查等待表状态
    try:
        waiting_sql = "SELECT position_id, member_id, total_open_amount, waiting_since FROM incomplete_positions_waiting ORDER BY position_id"
        waiting_results = processor.db_manager.execute_sql(waiting_sql)
        
        print(f"\n📋 等待表状态: {len(waiting_results)} 个等待订单")
        for row in waiting_results:
            print(f"   - {row['position_id']}: 用户={row['member_id']}, 开仓金额={row['total_open_amount']}, 等待时间={row['waiting_since']}")
            
        # 🚀 修正验证逻辑：只有开仓数据的订单才应该进入等待表
        expected_waiting = ['TEST_INCOMPLETE_002']  # 只有这个应该在等待表中
        expected_complete = ['TEST_COMPLETE_001']   # 完整订单应该直接处理

        actual_waiting = [row['position_id'] for row in waiting_results]
        actual_complete = list(result.keys())

        print(f"\n🎯 验证结果:")
        print(f"   预期等待订单: {expected_waiting}")
        print(f"   实际等待订单: {actual_waiting}")
        print(f"   预期完整订单: {expected_complete}")
        print(f"   实际完整订单: {actual_complete}")

        waiting_correct = set(expected_waiting) == set(actual_waiting)
        complete_correct = set(expected_complete) == set(actual_complete)

        if waiting_correct and complete_correct:
            print("✅ 修复成功！增量模式逻辑完全正确")
            print("   - 完整订单直接处理，不进入等待表")
            print("   - 只有开仓数据的订单进入等待表")
            print("   - 只有平仓数据的订单被正确跳过（等待表不支持此类型）")
        else:
            if not waiting_correct:
                print("❌ 等待表状态不符合预期")
            if not complete_correct:
                print("❌ 完整订单处理不符合预期")
            
    except Exception as e:
        print(f"❌ 检查等待表失败: {e}")

if __name__ == "__main__":
    test_incremental_processing()
